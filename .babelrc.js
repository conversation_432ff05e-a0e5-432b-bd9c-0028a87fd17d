module.exports = {
  presets: [
    [
      'next/babel',
      {
        'preset-env': {
          // استهداف المتصفحات الحديثة فقط لتقليل polyfills
          targets: {
            browsers: [
              'last 2 Chrome versions',
              'last 2 Firefox versions',
              'last 2 Safari versions',
              'last 2 Edge versions',
              'not IE 11',
              'not IE_Mob 11'
            ]
          },
          // تقليل polyfills للمتصفحات الحديثة
          useBuiltIns: 'entry',
          corejs: 3,
          // تجنب تحويل الميزات المدعومة في المتصفحات الحديثة
          exclude: [
            'transform-typeof-symbol',
            'transform-unicode-regex',
            'transform-sticky-regex',
            'transform-new-target',
            'transform-modules-umd',
            'transform-modules-systemjs',
            'transform-modules-amd',
            'transform-literals'
          ]
        }
      }
    ]
  ],
  plugins: [
    // تحسينات إضافية للأداء
    ['babel-plugin-transform-remove-console', { exclude: ['error', 'warn'] }],
    
    // تحسين imports
    [
      'babel-plugin-import',
      {
        libraryName: 'lodash',
        libraryDirectory: '',
        camel2DashComponentName: false
      },
      'lodash'
    ]
  ],
  env: {
    production: {
      plugins: [
        // إزالة console.log في الإنتاج
        ['transform-remove-console', { exclude: ['error', 'warn'] }],
        
        // تحسين الكود
        'babel-plugin-transform-remove-debugger',
        
        // تحسين React
        ['babel-plugin-transform-react-remove-prop-types', { removeImport: true }]
      ]
    },
    development: {
      plugins: [
        // أدوات التطوير
        'babel-plugin-styled-components'
      ]
    }
  }
};
