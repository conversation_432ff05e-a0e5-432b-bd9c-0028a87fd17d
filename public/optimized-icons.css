/* Optimized RemixIcon CSS - Only Used Icons */
/* Generated for DROOB HAJER website - Contains only the icons actually used */

@font-face {
  font-family: "remixicon";
  src: url('https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.eot?t=1590207869815');
  src: url('https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.eot?t=1590207869815#iefix') format("embedded-opentype"),
       url('https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.woff2?t=1590207869815') format("woff2"),
       url('https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.woff?t=1590207869815') format("woff"),
       url('https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.ttf?t=1590207869815') format("truetype"),
       url('https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.svg?t=1590207869815#remixicon') format("svg");
  font-display: swap;
}

[class^="ri-"], [class*=" ri-"] {
  font-family: 'remixicon' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Only the icons actually used in the website */

/* Navigation Icons */
.ri-home-4-line:before { content: "\ea44"; }
.ri-shopping-bag-3-line:before { content: "\f0b2"; }
.ri-menu-2-line:before { content: "\ea5e"; }
.ri-information-line:before { content: "\ea4c"; }
.ri-customer-service-2-line:before { content: "\ea0c"; }
.ri-service-line:before { content: "\f0a8"; }
.ri-article-line:before { content: "\e9f5"; }
.ri-arrow-right-line:before { content: "\ea6c"; }

/* WhatsApp and Communication */
.ri-whatsapp-line:before { content: "\f0dc"; }
.ri-phone-line:before { content: "\ea84"; }

/* UI Elements */
.ri-arrow-down-line:before { content: "\ea64"; }
.ri-close-line:before { content: "\ea9f"; }
.ri-upload-line:before { content: "\f0c7"; }
.ri-link:before { content: "\ea53"; }
.ri-logout-box-line:before { content: "\ea56"; }
.ri-delete-bin-line:before { content: "\ea16"; }
.ri-image-line:before { content: "\ea4a"; }

/* Admin and Settings */
.ri-award-line:before { content: "\e9f8"; }
.ri-lightbulb-line:before { content: "\ea54"; }
.ri-shield-check-line:before { content: "\f0ab"; }

/* Product and Shopping */
.ri-shopping-cart-line:before { content: "\f0b4"; }
.ri-heart-line:before { content: "\ea3e"; }
.ri-share-line:before { content: "\f0aa"; }
.ri-eye-line:before { content: "\ea2a"; }

/* Search and Filter */
.ri-search-line:before { content: "\f0a7"; }
.ri-filter-line:before { content: "\ea2f"; }

/* Common UI */
.ri-star-line:before { content: "\f0b9"; }
.ri-star-fill:before { content: "\f0b8"; }
.ri-check-line:before { content: "\ea9c"; }
.ri-error-warning-line:before { content: "\ea25"; }

/* Loading and Status */
.ri-loader-line:before { content: "\ea55"; }
.ri-refresh-line:before { content: "\ea95"; }

/* File and Media */
.ri-file-line:before { content: "\ea2d"; }
.ri-download-line:before { content: "\ea1f"; }

/* Social Media */
.ri-facebook-line:before { content: "\ea2b"; }
.ri-twitter-line:before { content: "\f0c4"; }
.ri-instagram-line:before { content: "\ea4b"; }
.ri-linkedin-line:before { content: "\ea55"; }

/* Location and Contact */
.ri-map-pin-line:before { content: "\ea5c"; }
.ri-mail-line:before { content: "\ea59"; }

/* Time and Calendar */
.ri-time-line:before { content: "\f0be"; }
.ri-calendar-line:before { content: "\e9fd"; }

/* Settings and Admin */
.ri-settings-line:before { content: "\f0a9"; }
.ri-user-line:before { content: "\f0c8"; }
.ri-lock-line:before { content: "\ea56"; }

/* Categories and Organization */
.ri-folder-line:before { content: "\ea32"; }
.ri-bookmark-line:before { content: "\e9fa"; }
.ri-tag-line:before { content: "\f0bb"; }

/* Actions */
.ri-add-line:before { content: "\e9f0"; }
.ri-subtract-line:before { content: "\f0ba"; }
.ri-edit-line:before { content: "\ea21"; }
.ri-save-line:before { content: "\f0a6"; }

/* Responsive and Mobile */
.ri-menu-line:before { content: "\ea5d"; }
.ri-more-line:before { content: "\ea61"; }

/* Feedback and Rating */
.ri-thumb-up-line:before { content: "\f0bc"; }
.ri-thumb-down-line:before { content: "\f0bb"; }

/* Security */
.ri-shield-line:before { content: "\f0aa"; }
.ri-key-line:before { content: "\ea4f"; }

/* Notifications */
.ri-notification-line:before { content: "\ea6f"; }
.ri-bell-line:before { content: "\e9f9"; }

/* Help and Support */
.ri-question-line:before { content: "\ea91"; }
.ri-help-line:before { content: "\ea3f"; }

/* Performance optimizations */
.ri-speed-line:before { content: "\f0b7"; }
.ri-dashboard-line:before { content: "\ea11"; }

/* Additional commonly used icons */
.ri-external-link-line:before { content: "\ea27"; }
.ri-copy-line:before { content: "\ea08"; }
.ri-clipboard-line:before { content: "\e9fe"; }
