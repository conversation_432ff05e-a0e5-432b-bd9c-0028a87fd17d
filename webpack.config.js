const path = require('path');

module.exports = {
  // تحسين للمتصفحات الحديثة
  target: ['web', 'es2020'],
  
  // تحسين الـ bundle
  optimization: {
    // تقسيم الكود
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        // مكتبات خارجية
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
          priority: 10
        },
        // مكونات React
        react: {
          test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
          name: 'react',
          chunks: 'all',
          priority: 20
        },
        // أيقونات
        icons: {
          test: /[\\/]node_modules[\\/].*icon.*[\\/]/,
          name: 'icons',
          chunks: 'all',
          priority: 15
        },
        // CSS
        styles: {
          test: /\.css$/,
          name: 'styles',
          chunks: 'all',
          priority: 5
        }
      }
    },
    
    // تحسين الكود
    usedExports: true,
    sideEffects: false,
    
    // تصغير الكود
    minimize: true,
    minimizer: [
      // استخدام SWC بدلاً من Terser للسرعة
      '...'
    ]
  },
  
  // تحسين الـ resolve
  resolve: {
    // ترتيب أولوية الملفات
    extensions: ['.tsx', '.ts', '.jsx', '.js', '.json'],
    
    // aliases للمسارات
    alias: {
      '@': path.resolve(__dirname, './'),
      '@components': path.resolve(__dirname, './components'),
      '@lib': path.resolve(__dirname, './lib'),
      '@hooks': path.resolve(__dirname, './hooks'),
      '@utils': path.resolve(__dirname, './utils'),
      '@types': path.resolve(__dirname, './types'),
      '@styles': path.resolve(__dirname, './styles'),
      '@public': path.resolve(__dirname, './public')
    },
    
    // تحسين البحث عن الملفات
    modules: ['node_modules', path.resolve(__dirname, './')],
    
    // تحسين symlinks
    symlinks: false
  },
  
  // تحسين الـ module
  module: {
    rules: [
      // TypeScript/JavaScript
      {
        test: /\.(ts|tsx|js|jsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'swc-loader',
          options: {
            jsc: {
              target: 'es2020',
              parser: {
                syntax: 'typescript',
                tsx: true,
                decorators: false,
                dynamicImport: true
              },
              transform: {
                react: {
                  runtime: 'automatic',
                  development: process.env.NODE_ENV === 'development'
                }
              },
              // تحسين للمتصفحات الحديثة
              externalHelpers: false,
              keepClassNames: false,
              preserveAllComments: false
            },
            env: {
              targets: {
                browsers: [
                  'last 2 Chrome versions',
                  'last 2 Firefox versions',
                  'last 2 Safari versions',
                  'last 2 Edge versions'
                ]
              },
              // تجنب polyfills غير ضرورية
              mode: 'entry',
              coreJs: '3.30'
            },
            minify: process.env.NODE_ENV === 'production'
          }
        }
      },
      
      // CSS
      {
        test: /\.css$/,
        use: [
          'style-loader',
          {
            loader: 'css-loader',
            options: {
              importLoaders: 1,
              modules: {
                auto: true,
                localIdentName: process.env.NODE_ENV === 'production' 
                  ? '[hash:base64:5]' 
                  : '[name]__[local]__[hash:base64:5]'
              }
            }
          },
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                plugins: [
                  'tailwindcss',
                  'autoprefixer',
                  // تحسين CSS
                  ['cssnano', {
                    preset: ['default', {
                      discardComments: { removeAll: true },
                      normalizeWhitespace: true,
                      colormin: true,
                      convertValues: true,
                      discardDuplicates: true,
                      discardEmpty: true,
                      discardOverridden: true,
                      discardUnused: true,
                      mergeIdents: true,
                      mergeLonghand: true,
                      mergeRules: true,
                      minifyFontValues: true,
                      minifyGradients: true,
                      minifyParams: true,
                      minifySelectors: true,
                      normalizeCharset: true,
                      normalizeDisplayValues: true,
                      normalizePositions: true,
                      normalizeRepeatStyle: true,
                      normalizeString: true,
                      normalizeTimingFunctions: true,
                      normalizeUnicode: true,
                      normalizeUrl: true,
                      orderedValues: true,
                      reduceIdents: true,
                      reduceInitial: true,
                      reduceTransforms: true,
                      svgo: true,
                      uniqueSelectors: true
                    }]
                  }]
                ]
              }
            }
          }
        ]
      }
    ]
  },
  
  // تحسين الأداء
  performance: {
    maxAssetSize: 250000,
    maxEntrypointSize: 250000,
    hints: process.env.NODE_ENV === 'production' ? 'warning' : false
  },
  
  // تحسين الـ cache
  cache: {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename]
    }
  },
  
  // تحسين الـ stats
  stats: {
    preset: 'minimal',
    moduleTrace: true,
    errorDetails: true
  }
};
