'use client';

import { useEffect } from 'react';

/**
 * مكون لتحسين تحميل CSS وتقليل CSS غير المستخدم
 */
const OptimizedCSS: React.FC = () => {
  useEffect(() => {
    // تحميل CSS غير الحرج بعد تحميل الصفحة
    const loadNonCriticalCSS = () => {
      // قائمة CSS غير الحرج
      const nonCriticalCSS = [
        {
          href: '/optimized-icons.css',
          id: 'optimized-icons'
        }
      ];

      nonCriticalCSS.forEach(({ href, id }) => {
        // تحقق من عدم وجود الملف مسبقاً
        if (!document.getElementById(id)) {
          const link = document.createElement('link');
          link.id = id;
          link.rel = 'stylesheet';
          link.href = href;
          link.media = 'print'; // تحميل بدون حظر
          link.onload = function() {
            (this as HTMLLinkElement).media = 'all'; // تفعيل بعد التحميل
          };
          
          // إضافة fallback للمتصفحات القديمة
          const noscript = document.createElement('noscript');
          const fallbackLink = document.createElement('link');
          fallbackLink.rel = 'stylesheet';
          fallbackLink.href = href;
          noscript.appendChild(fallbackLink);
          
          document.head.appendChild(link);
          document.head.appendChild(noscript);
        }
      });
    };

    // إزالة CSS غير المستخدم
    const removeUnusedCSS = () => {
      // إزالة RemixIcon الكامل إذا كان موجوداً
      const remixIconLink = document.querySelector('link[href*="remixicon.css"]');
      if (remixIconLink) {
        remixIconLink.remove();
        console.log('🗑️ Removed unused RemixIcon CSS');
      }

      // إزالة أي CSS غير مستخدم آخر
      const unusedCSS = [
        'link[href*="bootstrap"]',
        'link[href*="fontawesome"]',
        'link[href*="material-icons"]'
      ];

      unusedCSS.forEach(selector => {
        const element = document.querySelector(selector);
        if (element) {
          element.remove();
          console.log(`🗑️ Removed unused CSS: ${selector}`);
        }
      });
    };

    // تحسين CSS الموجود
    const optimizeExistingCSS = () => {
      // إضافة media queries للطباعة
      const styleSheets = document.querySelectorAll('link[rel="stylesheet"]');
      styleSheets.forEach(sheet => {
        const link = sheet as HTMLLinkElement;
        if (!link.media || link.media === 'all') {
          // إضافة media query مناسب
          if (link.href.includes('print')) {
            link.media = 'print';
          } else if (link.href.includes('mobile')) {
            link.media = '(max-width: 768px)';
          }
        }
      });
    };

    // تحسين الخطوط
    const optimizeFonts = () => {
      // preload للخطوط المهمة
      const criticalFonts = [
        {
          href: 'https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1l_6gLuvPDQ.woff2',
          type: 'font/woff2'
        },
        {
          href: 'https://fonts.gstatic.com/s/poppins/v20/pxiEyp8kv8JHgFVrJJfecg.woff2',
          type: 'font/woff2'
        }
      ];

      criticalFonts.forEach(font => {
        const existingPreload = document.querySelector(`link[href="${font.href}"]`);
        if (!existingPreload) {
          const link = document.createElement('link');
          link.rel = 'preload';
          link.as = 'font';
          link.type = font.type;
          link.href = font.href;
          link.crossOrigin = 'anonymous';
          document.head.appendChild(link);
        }
      });
    };

    // تحسين أداء CSS
    const optimizeCSSPerformance = () => {
      // إضافة will-change للعناصر المتحركة
      const animatedElements = document.querySelectorAll('.animate, .transition, .hover\\:');
      animatedElements.forEach(element => {
        (element as HTMLElement).style.willChange = 'transform, opacity';
      });

      // تحسين الصور للـ CSS
      const images = document.querySelectorAll('img');
      images.forEach(img => {
        if (!img.style.aspectRatio && img.width && img.height) {
          img.style.aspectRatio = `${img.width}/${img.height}`;
        }
      });
    };

    // تنظيف CSS القديم
    const cleanupOldCSS = () => {
      // إزالة inline styles غير ضرورية
      const elementsWithInlineStyles = document.querySelectorAll('[style]');
      elementsWithInlineStyles.forEach(element => {
        const style = (element as HTMLElement).style;
        
        // إزالة styles افتراضية
        if (style.display === 'block' && element.tagName === 'DIV') {
          style.removeProperty('display');
        }
        
        if (style.position === 'static') {
          style.removeProperty('position');
        }
        
        if (style.zIndex === '0' || style.zIndex === 'auto') {
          style.removeProperty('z-index');
        }
      });
    };

    // تشغيل التحسينات
    const runOptimizations = () => {
      removeUnusedCSS();
      optimizeExistingCSS();
      optimizeFonts();
      optimizeCSSPerformance();
      
      // تأجيل التحسينات غير الحرجة
      setTimeout(() => {
        loadNonCriticalCSS();
        cleanupOldCSS();
      }, 100);
    };

    // تشغيل التحسينات عند تحميل الصفحة
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', runOptimizations);
    } else {
      runOptimizations();
    }

    // تنظيف عند إلغاء التحميل
    return () => {
      document.removeEventListener('DOMContentLoaded', runOptimizations);
    };
  }, []);

  return null; // هذا المكون لا يعرض أي محتوى
};

export default OptimizedCSS;
